package com.example.myapplication.notifications;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Build;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import com.example.myapplication.MainActivity;
import com.example.myapplication.R;
import com.example.myapplication.TaskDetailActivity;

/**
 * Helper class để quản lý việc tạo và hiển thị notification
 */
public class NotificationHelper {
    
    private static final String CHANNEL_ID = "task_reminders";
    private static final String CHANNEL_NAME = "Task Reminders";
    private static final String CHANNEL_DESCRIPTION = "Notifications for task reminders";
    private static final int NOTIFICATION_ID_BASE = 1000;
    
    private Context context;
    private NotificationManagerCompat notificationManager;
    
    public NotificationHelper(Context context) {
        this.context = context;
        this.notificationManager = NotificationManagerCompat.from(context);
        createNotificationChannel();
    }
    
    /**
     * Tạo notification channel (cần thiết cho Android 8.0+)
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            channel.enableLights(true);
            channel.enableVibration(true);
            channel.setVibrationPattern(new long[]{0, 1000, 500, 1000});
            
            NotificationManager manager = context.getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
    
    /**
     * Hiển thị notification nhắc nhở task
     */
    public void showTaskReminderNotification(int taskId, String taskTitle, String taskDescription) {
        // Intent để mở TaskDetailActivity khi tap vào notification
        Intent intent = new Intent(context, TaskDetailActivity.class);
        intent.putExtra("task_id", taskId);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context, 
            taskId, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        // Tạo notification
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification) // Cần tạo icon này
            .setLargeIcon(BitmapFactory.decodeResource(context.getResources(), R.mipmap.ic_launcher))
            .setContentTitle("Nhắc nhở Task")
            .setContentText(taskTitle + " sẽ bắt đầu trong 30 phút")
            .setStyle(new NotificationCompat.BigTextStyle()
                .bigText(taskTitle + " sẽ bắt đầu trong 30 phút\n" + 
                        (taskDescription != null && !taskDescription.isEmpty() ? taskDescription : "")))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent);
        
        // Thêm action buttons
        addNotificationActions(builder, taskId);
        
        // Hiển thị notification
        int notificationId = NOTIFICATION_ID_BASE + taskId;
        notificationManager.notify(notificationId, builder.build());
    }
    
    /**
     * Thêm các action buttons vào notification
     */
    private void addNotificationActions(NotificationCompat.Builder builder, int taskId) {
        // Action: Đánh dấu hoàn thành
        Intent completeIntent = new Intent(context, TaskActionReceiver.class);
        completeIntent.setAction("COMPLETE_TASK");
        completeIntent.putExtra("task_id", taskId);
        
        PendingIntent completePendingIntent = PendingIntent.getBroadcast(
            context,
            taskId * 10 + 1,
            completeIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        builder.addAction(R.drawable.ic_check, "Hoàn thành", completePendingIntent);
        
        // Action: Hoãn 15 phút
        Intent snoozeIntent = new Intent(context, TaskActionReceiver.class);
        snoozeIntent.setAction("SNOOZE_TASK");
        snoozeIntent.putExtra("task_id", taskId);
        
        PendingIntent snoozePendingIntent = PendingIntent.getBroadcast(
            context,
            taskId * 10 + 2,
            snoozeIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        builder.addAction(R.drawable.ic_snooze, "Hoãn 15p", snoozePendingIntent);
    }
    
    /**
     * Hủy notification
     */
    public void cancelNotification(int taskId) {
        int notificationId = NOTIFICATION_ID_BASE + taskId;
        notificationManager.cancel(notificationId);
    }
    
    /**
     * Hủy tất cả notification
     */
    public void cancelAllNotifications() {
        notificationManager.cancelAll();
    }
    
    /**
     * Kiểm tra xem notification có được bật không
     */
    public boolean areNotificationsEnabled() {
        return notificationManager.areNotificationsEnabled();
    }
    
    /**
     * Hiển thị notification test
     */
    public void showTestNotification() {
        showTaskReminderNotification(0, "Test Task", "Đây là notification test");
    }
}
