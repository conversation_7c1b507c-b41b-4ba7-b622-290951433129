package com.example.myapplication.notifications;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.widget.Toast;

import com.example.myapplication.database.TodoDatabase;
import com.example.myapplication.database.entities.TaskEntity;

import java.util.Calendar;
import java.util.Date;

/**
 * BroadcastReceiver để xử lý các action từ notification (Complete, Snooze)
 */
public class TaskActionReceiver extends BroadcastReceiver {
    
    private static final String TAG = "TaskActionReceiver";
    public static final String ACTION_COMPLETE_TASK = "COMPLETE_TASK";
    public static final String ACTION_SNOOZE_TASK = "SNOOZE_TASK";
    public static final String EXTRA_TASK_ID = "task_id";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        int taskId = intent.getIntExtra(EXTRA_TASK_ID, -1);
        
        Log.d(TAG, "Received action: " + action + " for task ID: " + taskId);
        
        if (taskId == -1) {
            Log.e(TAG, "Invalid task ID");
            return;
        }
        
        switch (action) {
            case ACTION_COMPLETE_TASK:
                handleCompleteTask(context, taskId);
                break;
            case ACTION_SNOOZE_TASK:
                handleSnoozeTask(context, taskId);
                break;
            default:
                Log.w(TAG, "Unknown action: " + action);
        }
    }
    
    /**
     * Xử lý việc đánh dấu task hoàn thành
     */
    private void handleCompleteTask(Context context, int taskId) {
        new Thread(() -> {
            try {
                TodoDatabase database = TodoDatabase.getDatabase(context);
                TaskEntity task = database.taskDao().getTaskById(taskId);
                
                if (task != null && !task.isCompleted()) {
                    // Đánh dấu task hoàn thành
                    task.setCompleted(true);
                    database.taskDao().updateTask(task);
                    
                    // Hủy notification
                    NotificationHelper notificationHelper = new NotificationHelper(context);
                    notificationHelper.cancelNotification(taskId);
                    
                    // Hủy alarm nếu có
                    TaskSchedulerService.cancelTaskReminder(context, taskId);
                    
                    // Hiển thị toast
                    showToast(context, "Task đã được đánh dấu hoàn thành");
                    
                    Log.d(TAG, "Task completed: " + task.getTitle());
                } else {
                    Log.w(TAG, "Task not found or already completed: " + taskId);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error completing task", e);
            }
        }).start();
    }
    
    /**
     * Xử lý việc hoãn task 15 phút
     */
    private void handleSnoozeTask(Context context, int taskId) {
        new Thread(() -> {
            try {
                TodoDatabase database = TodoDatabase.getDatabase(context);
                TaskEntity task = database.taskDao().getTaskById(taskId);
                
                if (task != null && !task.isCompleted()) {
                    // Hủy notification hiện tại
                    NotificationHelper notificationHelper = new NotificationHelper(context);
                    notificationHelper.cancelNotification(taskId);
                    
                    // Tạo alarm mới sau 15 phút
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.MINUTE, 15);
                    
                    TaskSchedulerService.scheduleTaskReminder(
                        context, 
                        taskId, 
                        calendar.getTimeInMillis()
                    );
                    
                    // Hiển thị toast
                    showToast(context, "Nhắc nhở đã được hoãn 15 phút");
                    
                    Log.d(TAG, "Task snoozed for 15 minutes: " + task.getTitle());
                } else {
                    Log.w(TAG, "Task not found or already completed: " + taskId);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error snoozing task", e);
            }
        }).start();
    }
    
    /**
     * Hiển thị toast message trên UI thread
     */
    private void showToast(Context context, String message) {
        // Post to main thread để hiển thị toast
        android.os.Handler mainHandler = new android.os.Handler(context.getMainLooper());
        mainHandler.post(() -> {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
        });
    }
}
