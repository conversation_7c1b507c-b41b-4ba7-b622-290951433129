package com.example.myapplication.notifications;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.example.myapplication.database.TodoDatabase;
import com.example.myapplication.database.entities.TaskEntity;

/**
 * BroadcastReceiver để nhận alarm và hiển thị notification khi đến thời gian nhắc nhở
 */
public class TaskReminderReceiver extends BroadcastReceiver {
    
    private static final String TAG = "TaskReminderReceiver";
    public static final String ACTION_TASK_REMINDER = "com.example.myapplication.TASK_REMINDER";
    public static final String EXTRA_TASK_ID = "task_id";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "Received broadcast: " + intent.getAction());
        
        if (ACTION_TASK_REMINDER.equals(intent.getAction())) {
            int taskId = intent.getIntExtra(EXTRA_TASK_ID, -1);
            
            if (taskId != -1) {
                Log.d(TAG, "Processing task reminder for task ID: " + taskId);
                handleTaskReminder(context, taskId);
            } else {
                Log.e(TAG, "Invalid task ID received");
            }
        }
    }
    
    /**
     * Xử lý việc hiển thị notification nhắc nhở task
     */
    private void handleTaskReminder(Context context, int taskId) {
        // Chạy trong background thread để truy vấn database
        new Thread(() -> {
            try {
                TodoDatabase database = TodoDatabase.getDatabase(context);
                TaskEntity task = database.taskDao().getTaskById(taskId);
                
                if (task != null && !task.isCompleted()) {
                    Log.d(TAG, "Showing notification for task: " + task.getTitle());
                    
                    // Tạo và hiển thị notification
                    NotificationHelper notificationHelper = new NotificationHelper(context);
                    notificationHelper.showTaskReminderNotification(
                        task.getId(),
                        task.getTitle(),
                        task.getDescription()
                    );
                    
                    // Log để debug
                    Log.d(TAG, "Notification shown successfully for task: " + task.getTitle());
                } else {
                    Log.w(TAG, "Task not found or already completed: " + taskId);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error handling task reminder", e);
            }
        }).start();
    }
}
