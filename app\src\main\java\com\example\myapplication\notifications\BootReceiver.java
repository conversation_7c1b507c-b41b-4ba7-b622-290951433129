package com.example.myapplication.notifications;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.example.myapplication.database.TodoDatabase;
import com.example.myapplication.database.entities.TaskEntity;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * BroadcastReceiver để khôi phục alarm sau khi khởi động lại thiết bị
 */
public class BootReceiver extends BroadcastReceiver {
    
    private static final String TAG = "BootReceiver";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        Log.d(TAG, "Received broadcast: " + action);
        
        if (Intent.ACTION_BOOT_COMPLETED.equals(action) ||
            Intent.ACTION_MY_PACKAGE_REPLACED.equals(action) ||
            Intent.ACTION_PACKAGE_REPLACED.equals(action)) {
            
            Log.d(TAG, "Rescheduling task reminders after boot/update");
            rescheduleTaskReminders(context);
        }
    }
    
    /**
     * <PERSON><PERSON><PERSON><PERSON> phục tất cả alarm cho các task chưa hoàn thành
     */
    private void rescheduleTaskReminders(Context context) {
        new Thread(() -> {
            try {
                TodoDatabase database = TodoDatabase.getDatabase(context);
                
                // Lấy tất cả task chưa hoàn thành có startTime
                List<TaskEntity> incompleteTasks = database.taskDao().getIncompleteTasksWithStartTime();
                
                Date now = new Date();
                Calendar calendar = Calendar.getInstance();
                
                int rescheduledCount = 0;
                
                for (TaskEntity task : incompleteTasks) {
                    if (task.getStartTime() != null) {
                        // Tính thời gian nhắc nhở (30 phút trước startTime)
                        calendar.setTime(task.getStartTime());
                        calendar.add(Calendar.MINUTE, -30);
                        Date reminderTime = calendar.getTime();
                        
                        // Chỉ schedule nếu thời gian nhắc nhở chưa qua
                        if (reminderTime.after(now)) {
                            TaskSchedulerService.scheduleTaskReminder(
                                context,
                                task.getId(),
                                reminderTime.getTime()
                            );
                            rescheduledCount++;
                            
                            Log.d(TAG, "Rescheduled reminder for task: " + task.getTitle() + 
                                  " at " + reminderTime);
                        }
                    }
                }
                
                Log.d(TAG, "Rescheduled " + rescheduledCount + " task reminders");
                
            } catch (Exception e) {
                Log.e(TAG, "Error rescheduling task reminders", e);
            }
        }).start();
    }
}
