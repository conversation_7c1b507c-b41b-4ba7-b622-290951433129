<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header v<PERSON><PERSON> thông tin user -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/header_gradient"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- User Info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- Avatar -->
                <TextView
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:text="👤"
                    android:textSize="30sp"
                    android:gravity="center"
                    android:background="@drawable/circle_background"
                    android:backgroundTint="@android:color/white"
                    android:layout_marginEnd="16dp" />

                <!-- User Details -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_user_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tên người dùng"
                        android:textColor="@android:color/white"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_user_email"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="<EMAIL>"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        android:alpha="0.8"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- Thống kê Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📊 Thống kê"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="12dp" />

            <!-- Statistics Cards Row 1 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <!-- Total Tasks Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="6dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="#E3F2FD">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📋"
                            android:textSize="28sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tv_total_tasks"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            android:textColor="#1976D2" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Tổng nhiệm vụ"
                            android:textSize="11sp"
                            android:textColor="#666666"
                            android:textStyle="bold"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Completed Tasks Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginStart="6dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="#E8F5E8">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="✅"
                            android:textSize="28sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tv_completed_tasks"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            android:textColor="#388E3C" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Đã hoàn thành"
                            android:textSize="11sp"
                            android:textColor="#666666"
                            android:textStyle="bold"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Statistics Cards Row 2 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <!-- Pending Tasks Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="6dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="#FFF3E0">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⏳"
                            android:textSize="28sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tv_pending_tasks"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            android:textColor="#F57C00" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Chưa hoàn thành"
                            android:textSize="11sp"
                            android:textColor="#666666"
                            android:textStyle="bold"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Completion Rate Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="120dp"
                    android:layout_weight="1"
                    android:layout_marginStart="6dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="#F3E5F5">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:padding="16dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📈"
                            android:textSize="28sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tv_completion_percentage"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0%"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            android:textColor="#7B1FA2" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Tỷ lệ hoàn thành"
                            android:textSize="11sp"
                            android:textColor="#666666"
                            android:textStyle="bold"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>



            <!-- Categories Stats -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                app:cardBackgroundColor="#FAFAFA">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📊"
                            android:textSize="20sp"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Thống kê theo danh mục"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="#333333" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_category_stats"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- Cài đặt Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="⚙️ Cài đặt"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:layout_marginBottom="12dp" />

            <!-- Settings Options -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Edit Profile -->
                    <LinearLayout
                        android:id="@+id/layout_change_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical"
                        android:background="?attr/selectableItemBackground">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="👤"
                            android:textSize="20sp"
                            android:layout_marginEnd="16dp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Chỉnh sửa hồ sơ"
                                android:textSize="16sp"
                                android:textColor="#333333" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Thay đổi tên, email và mật khẩu"
                                android:textSize="12sp"
                                android:textColor="#666666"
                                android:layout_marginTop="2dp" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=">"
                            android:textSize="16sp"
                            android:textColor="#999999" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E0E0E0"
                        android:layout_marginStart="56dp" />

                    <!-- Notifications -->
                    <LinearLayout
                        android:id="@+id/layout_notifications"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical"
                        android:background="?attr/selectableItemBackground">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🔔"
                            android:textSize="20sp"
                            android:layout_marginEnd="16dp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Thông báo"
                                android:textSize="16sp"
                                android:textColor="#333333" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Cài đặt thông báo nhắc nhở"
                                android:textSize="12sp"
                                android:textColor="#666666"
                                android:layout_marginTop="2dp" />

                        </LinearLayout>

                        <Switch
                            android:id="@+id/switch_notifications"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E0E0E0"
                        android:layout_marginStart="56dp" />

                    <!-- Logout -->
                    <LinearLayout
                        android:id="@+id/layout_logout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical"
                        android:background="?attr/selectableItemBackground">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🚪"
                            android:textSize="20sp"
                            android:layout_marginEnd="16dp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Đăng xuất"
                                android:textSize="16sp"
                                android:textColor="#F44336" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Thoát khỏi tài khoản hiện tại"
                                android:textSize="12sp"
                                android:textColor="#666666"
                                android:layout_marginTop="2dp" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=">"
                            android:textSize="16sp"
                            android:textColor="#999999" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
