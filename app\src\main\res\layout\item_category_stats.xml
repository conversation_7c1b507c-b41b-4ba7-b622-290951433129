<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="#FFFFFF">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Category Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_category_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="💼 Công việc"
                android:textSize="15sp"
                android:textStyle="bold"
                android:textColor="#333333" />

            <TextView
                android:id="@+id/tv_percentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="75%"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/primary"
                android:background="@drawable/circle_background"
                android:backgroundTint="#E3F2FD"
                android:padding="6dp"
                android:minWidth="40dp"
                android:gravity="center" />

        </LinearLayout>

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:progress="75"
            android:max="100"
            android:progressTint="@color/primary"
            android:progressBackgroundTint="#E0E0E0"
            android:layout_marginBottom="6dp" />

        <!-- Task Count -->
        <TextView
            android:id="@+id/tv_task_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3/4 nhiệm vụ"
            android:textSize="12sp"
            android:textColor="#666666"
            android:textStyle="bold" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
